<template>
  <div class="ai-help-page">
    <!-- Hero Section -->
    <section class="ai-help-hero">
      <div class="site-container">
        <div class="hero-content">
          <h1 class="page-title">AI Help & Skill Guides</h1>
          <p class="hero-subtitle">
            Sometimes the tool is fine; we just need better fundamentals. Frame Check pairs reality-checks
            with skill-guides on prompt crafting, workflow design, and more.
          </p>
          <p class="hero-description">
            Build lasting capabilities with step-by-step guides that focus on substance, not spectacle.
          </p>
        </div>
      </div>
    </section>

    <!-- Guide Categories -->
    <section class="guide-categories">
      <div class="site-container">
        <div class="categories-grid">

          <!-- Prompt Crafting -->
          <div class="category-card">
            <div class="category-icon">🎯</div>
            <h2 class="category-title">Prompt Crafting</h2>
            <p class="category-description">
              Master the fundamentals of effective AI communication. Learn frameworks that work across different models and use cases.
            </p>
            <div class="category-guides">
              <RouterLink to="/ai-help/cstpit-framework" class="guide-item guide-link">
                <h3 class="guide-title">The CSTPIT Framework</h3>
                <p class="guide-description">Context, Specific Task, Parameters, Input, Tone - a systematic approach to prompt engineering.</p>
                <span class="guide-status">Available</span>
              </RouterLink>
              <div class="guide-item">
                <h3 class="guide-title">Chain-of-Thought Prompting</h3>
                <p class="guide-description">Get better reasoning from AI by showing your work step-by-step.</p>
                <span class="guide-status coming-soon">Coming Soon</span>
              </div>
            </div>
          </div>

          <!-- Workflow Design -->
          <div class="category-card">
            <div class="category-icon">⚙️</div>
            <h2 class="category-title">Workflow Design</h2>
            <p class="category-description">
              Design AI-assisted workflows that genuinely save hours without breaking when you need them most.
            </p>
            <div class="category-guides">
              <div class="guide-item">
                <h3 class="guide-title">Automation That Actually Works</h3>
                <p class="guide-description">Principles for building reliable AI workflows that won't blow up at 2 a.m.</p>
                <span class="guide-status coming-soon">Coming Soon</span>
              </div>
              <div class="guide-item">
                <h3 class="guide-title">Human-AI Collaboration Patterns</h3>
                <p class="guide-description">When to use AI, when to stay human, and how to combine both effectively.</p>
                <span class="guide-status coming-soon">Coming Soon</span>
              </div>
            </div>
          </div>

          <!-- Tool Evaluation -->
          <div class="category-card">
            <div class="category-icon">🔍</div>
            <h2 class="category-title">Tool Evaluation</h2>
            <p class="category-description">
              Learn to separate AI gold from fool's gold with systematic evaluation frameworks.
            </p>
            <div class="category-guides">
              <div class="guide-item">
                <h3 class="guide-title">The Frame Check Method</h3>
                <p class="guide-description">How to evaluate AI tools for real ROI in time and money.</p>
                <span class="guide-status coming-soon">Coming Soon</span>
              </div>
              <div class="guide-item">
                <h3 class="guide-title">Privacy & Security Checklist</h3>
                <p class="guide-description">Essential questions to ask before trusting your data to any AI service.</p>
                <span class="guide-status coming-soon">Coming Soon</span>
              </div>
            </div>
          </div>

        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
.ai-help-page {
  min-height: 100vh;
}

/* Hero Section */
.ai-help-hero {
  padding: var(--space-12) 0 var(--space-8);
  background: linear-gradient(135deg, var(--color-base) 0%, rgba(245, 247, 250, 0.8) 100%);
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.page-title {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: var(--color-text);
  margin-bottom: var(--space-4);
  opacity: 0.9;
  font-style: italic;
}

.hero-description {
  font-size: 1.125rem;
  line-height: 1.6;
  color: var(--color-text);
  opacity: 0.8;
}

/* Guide Categories */
.guide-categories {
  padding: var(--space-10) 0;
}

.categories-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-8);
  max-width: 1200px;
  margin: 0 auto;
}

.category-card {
  background: white;
  border: 1px solid var(--color-mist);
  border-radius: 12px;
  padding: var(--space-6);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.category-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
}

.category-icon {
  font-size: 2.5rem;
  margin-bottom: var(--space-4);
}

.category-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-3);
}

.category-description {
  font-size: 1.125rem;
  line-height: 1.6;
  color: var(--color-text);
  margin-bottom: var(--space-6);
  opacity: 0.8;
}

.category-guides {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.guide-item {
  padding: var(--space-4);
  background: var(--color-base);
  border-radius: 8px;
  border: 1px solid var(--color-mist);
  transition: all 0.2s ease;
}

.guide-link {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}

.guide-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--color-fern);
}

.guide-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-text);
  margin-bottom: var(--space-2);
}

.guide-link:hover .guide-title {
  color: var(--color-kawakawa);
}

.guide-description {
  font-size: 1rem;
  line-height: 1.5;
  color: var(--color-text);
  margin-bottom: var(--space-3);
  opacity: 0.8;
}

.guide-status {
  display: inline-block;
  padding: var(--space-1) var(--space-3);
  background: var(--color-fern);
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 20px;
}

.guide-status.coming-soon {
  background: var(--color-mist);
  color: var(--color-text);
}

/* Responsive Design */
@media (min-width: 768px) {
  .categories-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .categories-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .category-card:hover,
  .guide-link:hover {
    transform: none;
  }
}
</style>
