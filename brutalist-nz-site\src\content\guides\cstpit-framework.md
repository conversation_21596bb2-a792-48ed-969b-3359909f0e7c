# The CSTPIT Framework for Effective Prompting

*A systematic approach to AI communication that works across different models and use cases.*

## What is CSTPIT?

CSTPIT is a prompt engineering framework that stands for:
- **C**ontext
- **S**pecific Task  
- **P**arameters
- **I**nput
- **T**one

This framework helps you structure prompts that get consistent, high-quality results from AI models.

## The Framework Breakdown

### Context (C)
Set the stage. Give the AI the background information it needs to understand the situation.

**Example:**
```
You are a senior software engineer reviewing code for a fintech startup. 
The codebase handles sensitive financial data and must meet strict security standards.
```

### Specific Task (S)
Be crystal clear about what you want the AI to do. Vague requests get vague results.

**Instead of:** "Help me with this code"
**Try:** "Review this Python function for security vulnerabilities and suggest specific improvements"

### Parameters (P)
Define the constraints, format, length, or specific requirements for the output.

**Example:**
```
- Provide exactly 3 suggestions
- Format as numbered list
- Include code examples
- Keep explanations under 50 words each
```

### Input (I)
Provide the actual content, data, or material the AI should work with.

**Example:**
```
Here's the code to review:

[paste your code here]
```

### Tone (T)
Specify the voice, style, or approach you want the AI to take.

**Examples:**
- "Explain like I'm a beginner"
- "Use a professional, technical tone"
- "Be encouraging but honest about weaknesses"
- "Write in a conversational, friendly style"

## CSTPIT in Action

Here's a complete example using the framework:

```
**Context:** You are an experienced technical writer creating documentation for a new API.

**Specific Task:** Write a getting-started guide for developers who want to integrate our payment processing API.

**Parameters:** 
- 500-800 words
- Include code examples in JavaScript
- Use markdown formatting
- Include at least one troubleshooting tip

**Input:** 
API endpoint: https://api.example.com/v1/payments
Required headers: Authorization, Content-Type
Main methods: POST /charge, GET /status/:id

**Tone:** Professional but approachable, assuming intermediate developer knowledge.
```

## Why CSTPIT Works

1. **Reduces ambiguity** - Each element forces you to be specific
2. **Improves consistency** - Same structure = more predictable results  
3. **Saves time** - Less back-and-forth refinement needed
4. **Works across models** - The structure adapts to different AI systems

## Common Mistakes to Avoid

- **Skipping Context:** The AI doesn't know your situation
- **Vague Tasks:** "Help me" isn't a task
- **No Parameters:** Without constraints, you get whatever the AI thinks is best
- **Missing Input:** The AI can't work with nothing
- **Ignoring Tone:** Default AI voice might not match your needs

## Quick Reference

When crafting any prompt, ask yourself:
- [ ] Have I provided enough **Context**?
- [ ] Is my **Specific Task** clear and actionable?
- [ ] Have I defined the **Parameters** for the output?
- [ ] Have I included all necessary **Input**?
- [ ] Have I specified the **Tone** I want?

## Practice Exercise

Try rewriting this weak prompt using CSTPIT:

**Weak prompt:** "Make this email better"

**Your CSTPIT version:**
- Context: [Your situation]
- Specific Task: [What exactly to improve]
- Parameters: [Format, length, constraints]
- Input: [The actual email]
- Tone: [How it should sound]

---

*This guide is part of the Frame Check AI Help series. For more practical AI guidance without the hype, subscribe to Frame Check.*
