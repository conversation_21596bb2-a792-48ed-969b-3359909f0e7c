<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const guideContent = ref('')
const guideTitle = ref('')
const isLoading = ref(true)
const error = ref('')

// Simple guide content mapping
const guides = {
  'cstpit-framework': {
    title: 'The CSTPIT Framework for Effective Prompting',
    content: `
# The CSTPIT Framework for Effective Prompting

*A systematic approach to AI communication that works across different models and use cases.*

## What is CSTPIT?

CSTPIT is a prompt engineering framework that stands for:
- **Context** - Set the stage with background information
- **Specific Task** - Be crystal clear about what you want
- **Parameters** - Define constraints, format, and requirements
- **Input** - Provide the actual content to work with
- **Tone** - Specify the voice and style you want

This framework helps you structure prompts that get consistent, high-quality results from AI models.

## The Framework Breakdown

### Context (C)
Set the stage. Give the AI the background information it needs to understand the situation.

**Example:**
\`\`\`
You are a senior software engineer reviewing code for a fintech startup.
The codebase handles sensitive financial data and must meet strict security standards.
\`\`\`

### Specific Task (S)
Be crystal clear about what you want the AI to do. Vague requests get vague results.

**Instead of:** "Help me with this code"
**Try:** "Review this Python function for security vulnerabilities and suggest specific improvements"

### Parameters (P)
Define the constraints, format, length, or specific requirements for the output.

**Example:**
\`\`\`
- Provide exactly 3 suggestions
- Format as numbered list
- Include code examples
- Keep explanations under 50 words each
\`\`\`

### Input (I)
Provide the actual content, data, or material the AI should work with.

### Tone (T)
Specify the voice, style, or approach you want the AI to take.

**Examples:**
- "Explain like I'm a beginner"
- "Use a professional, technical tone"
- "Be encouraging but honest about weaknesses"

## Why CSTPIT Works

1. **Reduces ambiguity** - Each element forces you to be specific
2. **Improves consistency** - Same structure = more predictable results
3. **Saves time** - Less back-and-forth refinement needed
4. **Works across models** - The structure adapts to different AI systems

## Quick Reference

When crafting any prompt, ask yourself:
- [ ] Have I provided enough **Context**?
- [ ] Is my **Specific Task** clear and actionable?
- [ ] Have I defined the **Parameters** for the output?
- [ ] Have I included all necessary **Input**?
- [ ] Have I specified the **Tone** I want?

---

*This guide is part of the Frame Check AI Help series. For more practical AI guidance without the hype, subscribe to Frame Check.*
    `
  }
}

// Simple markdown-like formatting
const formatContent = (content: string) => {
  return content
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/^- (.*$)/gm, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
    .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/^\[ \] (.*$)/gm, '<div class="checkbox"><input type="checkbox" disabled> $1</div>')
    .replace(/^\[x\] (.*$)/gm, '<div class="checkbox"><input type="checkbox" checked disabled> $1</div>')
    .replace(/\n\n/g, '</p><p>')
    .replace(/^(?!<[h|u|p|d|c])(.*$)/gm, '<p>$1</p>')
    .replace(/<p><\/p>/g, '')
    .replace(/<p>(<h[1-6]>)/g, '$1')
    .replace(/(<\/h[1-6]>)<\/p>/g, '$1')
    .replace(/<p>(<ul>)/g, '$1')
    .replace(/(<\/ul>)<\/p>/g, '$1')
    .replace(/<p>(<pre>)/g, '$1')
    .replace(/(<\/pre>)<\/p>/g, '$1')
    .replace(/<p>(<div)/g, '$1')
    .replace(/(<\/div>)<\/p>/g, '$1')
}

onMounted(() => {
  const slug = route.params.slug as string
  const guide = guides[slug as keyof typeof guides]

  if (guide) {
    guideTitle.value = guide.title
    guideContent.value = guide.content
  } else {
    error.value = 'Guide not found'
  }

  isLoading.value = false
})
</script>

<template>
  <div class="ai-guide-page">
    <div class="site-container">
      <div v-if="isLoading" class="loading">
        Loading guide...
      </div>

      <div v-else-if="error" class="error">
        <h1>Guide Not Found</h1>
        <p>{{ error }}</p>
        <RouterLink to="/ai-help" class="back-link">← Back to AI Help</RouterLink>
      </div>

      <article v-else class="guide-content">
        <header class="guide-header">
          <RouterLink to="/ai-help" class="back-link">← Back to AI Help</RouterLink>
          <h1 class="guide-title">{{ guideTitle }}</h1>
        </header>

        <div class="guide-body prose" v-html="formatContent(guideContent)"></div>

        <footer class="guide-footer">
          <div class="guide-meta">
            <p class="meta-text">
              Part of the <strong>Frame Check</strong> AI Help series
            </p>
            <div class="guide-actions">
              <RouterLink to="/articles" class="action-button primary">
                Read More Guides
              </RouterLink>
              <RouterLink to="/contact" class="action-button secondary">
                Ask a Question
              </RouterLink>
            </div>
          </div>
        </footer>
      </article>
    </div>
  </div>
</template>

<style scoped>
.ai-guide-page {
  min-height: 100vh;
  padding: var(--space-8) 0;
}

.loading, .error {
  text-align: center;
  padding: var(--space-12) 0;
}

.back-link {
  display: inline-block;
  color: var(--color-fern);
  text-decoration: none;
  font-weight: 500;
  margin-bottom: var(--space-4);
  transition: color 0.2s ease;
}

.back-link:hover {
  color: var(--color-kawakawa);
}

.guide-content {
  max-width: 800px;
  margin: 0 auto;
}

.guide-header {
  margin-bottom: var(--space-8);
}

.guide-title {
  font-family: 'Montserrat', sans-serif;
  font-size: clamp(2rem, 4vw, 2.5rem);
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-text);
  margin: 0;
}

.guide-body {
  margin-bottom: var(--space-10);
}

/* Prose Styles */
.prose :deep(h1) {
  font-family: 'Montserrat', sans-serif;
  font-size: 2rem;
  font-weight: 600;
  color: var(--color-text);
  margin: var(--space-8) 0 var(--space-4);
  line-height: 1.2;
}

.prose :deep(h2) {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text);
  margin: var(--space-6) 0 var(--space-3);
  line-height: 1.3;
}

.prose :deep(h3) {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-kawakawa);
  margin: var(--space-5) 0 var(--space-2);
  line-height: 1.4;
}

.prose :deep(p) {
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.prose :deep(ul) {
  margin: var(--space-4) 0;
  padding-left: var(--space-6);
}

.prose :deep(li) {
  font-size: 1.125rem;
  line-height: 1.6;
  color: var(--color-text);
  margin-bottom: var(--space-2);
}

.prose :deep(strong) {
  font-weight: 600;
  color: var(--color-kawakawa);
}

.prose :deep(em) {
  font-style: italic;
  color: var(--color-text);
}

.prose :deep(code) {
  background: var(--color-base);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.9em;
  color: var(--color-kawakawa);
}

.prose :deep(pre) {
  background: var(--color-charcoal);
  color: white;
  padding: var(--space-4);
  border-radius: 8px;
  overflow-x: auto;
  margin: var(--space-4) 0;
}

.prose :deep(pre code) {
  background: none;
  padding: 0;
  color: white;
  font-size: 0.9rem;
}

.prose :deep(.checkbox) {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin: var(--space-2) 0;
  font-size: 1.125rem;
  line-height: 1.6;
  color: var(--color-text);
}

.prose :deep(.checkbox input) {
  margin: 0;
}

.guide-footer {
  border-top: 1px solid var(--color-mist);
  padding-top: var(--space-6);
}

.guide-meta {
  text-align: center;
}

.meta-text {
  font-size: 1rem;
  color: var(--color-text);
  opacity: 0.8;
  margin-bottom: var(--space-6);
}

.guide-actions {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
  flex-wrap: wrap;
}

.action-button {
  display: inline-block;
  padding: var(--space-3) var(--space-6);
  text-decoration: none;
  font-weight: 500;
  border-radius: 4px;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.action-button.primary {
  background-color: var(--color-fern);
  color: white;
  border-color: var(--color-fern);
}

.action-button.primary:hover {
  background-color: var(--color-kawakawa);
  border-color: var(--color-kawakawa);
  transform: translateY(-2px);
}

.action-button.secondary {
  background-color: transparent;
  color: var(--color-fern);
  border-color: var(--color-fern);
}

.action-button.secondary:hover {
  background-color: var(--color-fern);
  color: white;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .guide-actions {
    flex-direction: column;
    align-items: center;
  }

  .action-button {
    width: 200px;
    text-align: center;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .action-button:hover {
    transform: none;
  }
}
</style>
